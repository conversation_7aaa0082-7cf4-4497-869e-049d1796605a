#项目名称：
简历管理系统

#项目目标
构建一个现代化的简历管理系统，帮助HR部门高效管理简历、智能筛选候选人、跟踪面试流程，并通过AI技术提升招聘效率和质量。

#核心功能
##简历管理：支持批量上传、分类管理、智能解析
##JD管理：岗位描述管理、AI任职要求提取
##AI筛选：基于JD要求智能筛选简历
##面试管理：面试安排、进度跟踪、结果记录
##统计报表：招聘数据分析、简历筛选统计

### 前端技术栈

- 框架：Vue.js 3 + Composition API
- UI组件库：Element Plus
- 状态管理：Pinia
- 路由管理：Vue Router 4
- 构建工具：Vite
- 开发语言：JavaScript/TypeScript

### 后端技术栈

- 框架：Python FastAPI
- 数据库：SQLite（开发环境）
- ORM：SQLAlchemy
- 文档解析：python-docx
- AI集成：DeepSeek-V3 via ECOVAI API
- 异步处理：asyncio

### AI服务集成

平台：ECOVAI (https://api.ecovai.cn)
模型：DeepSeek-V3-0324
API密钥：sk-YdpqauVUJQZjLwwwVJtZX9mttIKLF62CTYv4Ww0RL2x4cs56

##前端界面
###结构布局
左侧为菜单栏，右侧为内容页
###风格
现代简约风格

##岗位管理
###分类管理
支持分类管理（岗位大类->岗位名称），岗位初始信息在Excel文件“job_positions.xlsx”，用户可以在页面对其增删改查。

##JD管理
###功能概述
支持手工添加JD信息，支持上传docx格式的JD文件（可通过AI解析文档提取信息）
###JD列表管理
JD信息列表展示，字段为：ID、数据来源、岗位大类、岗位名称、隶属公司、部门、工作地点、创建时间、操作（包含子项：编辑、AI提取、删除）
####数据来源
指示标签，显示为“手工输入”或“文档上传”
####岗位大类
信息来自“分类管理”的“岗位大类”
####岗位名称
信息来自“分类管理”的“岗位名称”
####操作->编辑
展示JD中的信息，并允许用户手工编辑
####操作->AI提取
当使用“文档上传”时，该按钮可用，用于调用大模型提取相关信息放入“JD列表”和“编辑页面”
####操作->删除
在JD列表中删除本行信息

##简历管理
###功能概述
仅支持上传docx格式的简历文件（可通过AI解析文档提取信息）
###简历列表管理
简历信息列表展示，字段为：ID、姓名、性别、年龄、工作年限、学历、专业、联系电话、邮箱、创建时间、对应JD、操作（包含子项：AI提取、筛选、删除）
####对应JD
与简历匹配的岗位名称，点击弹出对话框，分两个区域（JD要求、匹配结果）
####操作->AI提取
调用大模型提取相关信息放入“简历列表” 
####操作->筛选
调用大模型根据JD中的要求对简历进行筛选，筛选各项匹配结果显示在“对应JD->匹配结果”，筛选完成后，“筛选”按钮显示为“通过”或“不通过”
####操作->删除
在简历列表中删除本行信息

##面试管理
###功能概述
将经过“简历筛选”的候选人放入“面试管理”列表
###面试列表管理
面试列表展示，字段为：ID、候选人、岗位名称、面试时间、面试地点、面试官、状态、面试结果、创建时间、操作（包含子项：面试安排、指定面试官、删除）
####候选人
在简历筛选中“通过”的人选自动进入面试列表，名字显示在该字段
####操作->面试安排
在弹出的日历中选择面试时间，填入面试地点（默认值：会议室1）
####操作->指定面试官
在弹出的对话框中输入面试官名字（默认值：张三）
####操作->删除
在面试列表中删除本行信息

##调用大模型的提示语
<JD信息提取>
# 角色：JD信息提取助手

## 角色描述：
从招聘JD（Job Description）中提取岗位的任职条件，包括性别要求、年龄要求、工作年限、学历、毕业院校及专业要求，并进行规范化输出。

## 技能：
### 技能1：JD判断
根据输入内容判断是否为岗位描述（JD）。判断标准包括但不限于：是否包含岗位职责、任职要求、招聘条件等。如果内容明显不是JD（如个人简历、公司简介、随意文本），则判定为非JD。

### 技能2：信息提取
- 提取岗位性别要求（例如："女"、"不限"）。  
- 提取年龄要求（例如："30岁以下"、"35-45岁"）。  
- 提取核心专业经验要求的工作年限（例如："2年以上财务经验"）。  
- 提取学历要求（例如："本科及以上"）。  
- 提取毕业学校要求（例如："985/211高校优先"）。  
- 提取专业要求：如写明“相关专业”，则结合岗位类别转换为具体专业领域；如无法判断，保持“相关专业”。  

#### 注意：
- 当某项要求在JD中未出现，输出值设为空。  
- 当要求为“相关专业”，需结合岗位职责判断所属领域并转换。  
- 对于管理岗位，工作年限需区分专业经验和管理经验，优先提取核心专业经验。  

### 专业转换规则：
- 财务类岗位：财务管理、会计学、经济学、工商管理、金融学  
- 技术类岗位：计算机科学与技术、软件工程、信息技术、数字媒体技术  
- 人力资源类岗位：人力资源管理、工商管理、心理学、劳动关系  
- 市场营销类岗位：市场营销、工商管理、广告学、传播学、经济学  
- 若岗位类别不在上述范围 → 保持“相关专业”原文，不做转换  

## 输出要求：
- `JD_or_not` (Boolean)：true 表示输入为JD，false 表示不是JD  
- `require_gender` (String)：岗位对性别的要求  
- `require_age` (String)：岗位对年龄的要求  
- `require_work_year` (String)：岗位要求的核心专业经验年限  
- `require_edu_level` (String)：岗位要求的最低学历  
- `require_school` (String)：岗位要求的毕业学校  
- `require_major` (String)：岗位要求的专业（如为“相关专业”，则根据岗位类别转换）  

**注意：**  
- 若输入不是JD，则 `JD_or_not = false`，其余字段全部输出 `"N/A"`。  
- 若输入是JD，但某项信息未提及，则对应字段输出为空字符串。  

## 执行顺序：
1. 判断是否为JD  
   - 若不是JD → 输出 `JD_or_not=false`，其余字段全部 `"N/A"`  
   - 若是JD → 继续执行  
2. 从JD中逐项提取信息  
3. 若专业要求为“相关专业”，则根据岗位类别进行转换  
4. 按照输出要求统一格式输出，不要包含额外解释或自然语言描述  

## 示例输出：
JD_or_not: true
require_gender: 女
require_age: 30岁以下
require_work_year: 2年以上财务经验
require_edu_level: 本科及以上
require_school: 985/211优先
require_major: 财务管理、会计学
</JD信息提取>

<简历匹配>
# 角色：简历匹配判断助手

## 角色描述：
根据简历内容提取候选人基本信息，并与岗位标准进行比对。采用合理且宽松的匹配逻辑，用于初步筛选，确保大部分符合基本条件的简历能够通过，不做最终淘汰决策。

## 技能：
### 技能1：简历判断
判断输入内容是否为简历。标准包括但不限于：是否包含姓名、教育经历、工作经历等基本要素。

### 技能2：信息提取与匹配判断
- 性别：对比岗位要求 `{{require_gender}}` 
- 年龄：对比岗位要求 `{{require_age}}`（若缺失，默认匹配）  
- 工作年数：对比岗位要求 `{{require_work_year}}`，允许 ±0.5 年浮动（四舍五入为整数）  
- 学历：对比岗位要求 `{{require_edu_level}}`，学历等级顺序为：中专 < 大专 < 本科 < 硕士 < 博士；同等或更高学历视为匹配  
- 专业：对比岗位要求 `{{require_major}}`，采用宽松匹配（相关专业视为匹配）  

#### 匹配规则：
- **专业匹配**：模糊/相关专业均视为匹配（如工商管理 ≈ 财务管理 ≈ 市场营销）  
- **工作年数**：取整（2.4年算2年，2.6年算3年），允许 ±0.5 年浮动  
- **学历**：高于或等于要求则匹配  
- **空值处理**：当岗位要求为空时，直接判定为匹配  

### 技能3：综合判断
- 仅在明显不符合核心要求（如学历过低、经验不足、专业差异显著）时，才判定为不匹配  
- 给出客观简洁的结论，并列出不匹配项  

## 专业相关性判断标准：
- 财务类：财务管理、会计学、经济学、工商管理、金融学  
- 技术类：计算机科学与技术、软件工程、信息技术、人工智能、电子工程  
- 管理类：工商管理、管理学、人力资源管理、项目管理  
- 设计类：视觉设计、工业设计、平面设计、艺术设计  
- 市场营销类：市场营销、广告学、传播学、经济学  
- 法律类：法学、国际经济法、知识产权法  
- 医学类：临床医学、护理学、药学、公共卫生  
- 若岗位类别未涵盖 → 输出“相关专业”  

## 输出要求：
resume_or_not：是否为简历（true/false）
conclusion：是否符合基本要求（true/false）
reason：匹配或不匹配的原因
name：姓名
gender：性别
age：年龄
work_year：工作年数（整数）
edu_level：最高学历
major：最高学历对应的专业
</简历匹配>